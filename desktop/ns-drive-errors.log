[ERROR] 2025/07/03 23:58:04 handlers.go:150: TraceID: d38e6dff-11c4-4d07-bb12-1bd961b0f480 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/03 23:58:04 handlers.go:154: TraceID: d38e6dff-11c4-4d07-bb12-1bd961b0f480 | Details: open : no such file or directory
[ERROR] 2025/07/03 23:58:04 handlers.go:150: TraceID: 7a350bd5-4c05-4701-bb3b-1badc41a93e9 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/03 23:58:04 handlers.go:154: TraceID: 7a350bd5-4c05-4701-bb3b-1badc41a93e9 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/03 23:58:04 handlers.go:159: TraceID: d38e6dff-11c4-4d07-bb12-1bd961b0f480 | Stack Trace:
goroutine 239 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000407770, 0x1400020e9a0, {0x1400047cb40?, 0x0?, 0x1400014e378?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000407770, {0x105ac8178?, 0x140003c6a50?}, {0x1400047cb40, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000c09c0?, {0x105ac8178?, 0x140003c6a50?}, {0x1400047cb40?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140004dea80)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:136 +0x52c
desktop/backend.(*App).GetConfigInfo(0x140004dea80)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:164 +0x20c
reflect.Value.call({0x105a70080?, 0x140004dea80?, 0x14000619c78?}, {0x10544e99b, 0x4}, {0x10649b500, 0x0, 0x105063ac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105a70080?, 0x140004dea80?, 0x106b4d188?}, {0x10649b500?, 0x140004b0000?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000250d20, {0x105ad8b18, 0x1400017a510}, {0x10649b500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 238
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/03 23:58:04 handlers.go:164: TraceID: d38e6dff-11c4-4d07-bb12-1bd961b0f480 | Underlying Error: open : no such file or directory
[ERROR] 2025/07/03 23:58:04 handlers.go:159: TraceID: 7a350bd5-4c05-4701-bb3b-1badc41a93e9 | Stack Trace:
goroutine 192 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.1/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000407770, 0x140004dc310, {0x1400016e320?, 0x0?, 0x140003f46f0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000407770, {0x105ac8178?, 0x140003ce3f0?}, {0x1400016e320, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000c09c0?, {0x105ac8178?, 0x140003ce3f0?}, {0x1400016e320?, 0x14000268bf0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140004dea80)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:136 +0x52c
desktop/backend.(*App).GetRemotes(0x140004dea80)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:201 +0x78
reflect.Value.call({0x105a70080?, 0x140004dea80?, 0x14000021c78?}, {0x10544e99b, 0x4}, {0x10649b500, 0x0, 0x105063ac4?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105a70080?, 0x140004dea80?, 0x14000021ce8?}, {0x10649b500?, 0x10490df00?, 0x104b9edc0?})
	/Users/<USER>/.goenv/versions/1.24.1/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000250e60, {0x105ad8b18, 0x140003ae5a0}, {0x10649b500, 0x0, 0x20?})
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 191
	/Users/<USER>/go/1.24.1/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/03 23:58:04 handlers.go:164: TraceID: 7a350bd5-4c05-4701-bb3b-1badc41a93e9 | Underlying Error: open .config/.profiles: no such file or directory
